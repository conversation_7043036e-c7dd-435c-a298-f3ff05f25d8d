#!/usr/bin/env python3
"""
SERP关键词分析器性能监控脚本

监控分析过程的性能指标，包括处理时间、成功率、API调用次数等。
"""

import asyncio
import time
import json
import csv
from typing import List, Dict, Any
from pathlib import Path
import logging

from modules import analyze_single_keyword

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        """初始化监控器"""
        self.metrics = {
            'total_keywords': 0,
            'successful_analyses': 0,
            'failed_analyses': 0,
            'total_time': 0,
            'average_time_per_keyword': 0,
            'total_search_results': 0,
            'average_search_results': 0,
            'api_calls': 0,
            'errors': []
        }
        
    async def benchmark_single_keyword(self, keyword: str) -> Dict[str, Any]:
        """基准测试单个关键词"""
        start_time = time.time()
        
        try:
            result = await analyze_single_keyword(keyword)
            
            analysis_time = time.time() - start_time
            
            # 更新指标
            self.metrics['total_keywords'] += 1
            self.metrics['successful_analyses'] += 1
            self.metrics['total_time'] += analysis_time
            self.metrics['total_search_results'] += result.get('search_results_count', 0)
            self.metrics['api_calls'] += 1  # 假设每次分析调用一次API
            
            return {
                'keyword': keyword,
                'success': True,
                'analysis_time': analysis_time,
                'search_results_count': result.get('search_results_count', 0),
                'confidence_score': result.get('confidence_score', 0),
                'error': None
            }
            
        except Exception as e:
            analysis_time = time.time() - start_time
            
            # 更新指标
            self.metrics['total_keywords'] += 1
            self.metrics['failed_analyses'] += 1
            self.metrics['total_time'] += analysis_time
            self.metrics['errors'].append({
                'keyword': keyword,
                'error': str(e),
                'time': analysis_time
            })
            
            return {
                'keyword': keyword,
                'success': False,
                'analysis_time': analysis_time,
                'search_results_count': 0,
                'confidence_score': 0,
                'error': str(e)
            }
            
    async def benchmark_multiple_keywords(self, keywords: List[str], delay: float = 3.0) -> List[Dict[str, Any]]:
        """基准测试多个关键词"""
        results = []
        
        logger.info(f"开始基准测试 {len(keywords)} 个关键词")
        
        for i, keyword in enumerate(keywords, 1):
            logger.info(f"测试关键词 {i}/{len(keywords)}: {keyword}")
            
            result = await self.benchmark_single_keyword(keyword)
            results.append(result)
            
            # 打印实时进度
            if result['success']:
                logger.info(f"✅ 成功 - 耗时: {result['analysis_time']:.2f}s, 结果数: {result['search_results_count']}")
            else:
                logger.error(f"❌ 失败 - 耗时: {result['analysis_time']:.2f}s, 错误: {result['error']}")
            
            # 添加延迟
            if i < len(keywords):
                logger.info(f"等待 {delay} 秒...")
                await asyncio.sleep(delay)
                
        # 计算平均值
        if self.metrics['total_keywords'] > 0:
            self.metrics['average_time_per_keyword'] = self.metrics['total_time'] / self.metrics['total_keywords']
            
        if self.metrics['successful_analyses'] > 0:
            self.metrics['average_search_results'] = self.metrics['total_search_results'] / self.metrics['successful_analyses']
            
        return results
        
    def generate_report(self) -> Dict[str, Any]:
        """生成性能报告"""
        success_rate = (self.metrics['successful_analyses'] / self.metrics['total_keywords'] * 100) if self.metrics['total_keywords'] > 0 else 0
        
        report = {
            'summary': {
                'total_keywords': self.metrics['total_keywords'],
                'successful_analyses': self.metrics['successful_analyses'],
                'failed_analyses': self.metrics['failed_analyses'],
                'success_rate': f"{success_rate:.1f}%",
                'total_time': f"{self.metrics['total_time']:.2f}s",
                'average_time_per_keyword': f"{self.metrics['average_time_per_keyword']:.2f}s",
                'total_search_results': self.metrics['total_search_results'],
                'average_search_results': f"{self.metrics['average_search_results']:.1f}",
                'estimated_api_calls': self.metrics['api_calls']
            },
            'errors': self.metrics['errors']
        }
        
        return report
        
    def save_report(self, report: Dict[str, Any], filename: str = "performance_report.json"):
        """保存性能报告"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        logger.info(f"性能报告已保存到: {filename}")
        
    def save_detailed_results(self, results: List[Dict[str, Any]], filename: str = "benchmark_results.csv"):
        """保存详细的基准测试结果"""
        if not results:
            return
            
        fieldnames = ['keyword', 'success', 'analysis_time', 'search_results_count', 'confidence_score', 'error']
        
        with open(filename, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(results)
            
        logger.info(f"详细结果已保存到: {filename}")


async def run_performance_test():
    """运行性能测试"""
    # 测试关键词列表
    test_keywords = [
        "machine learning",
        "artificial intelligence", 
        "data science",
        "python programming",
        "web development"
    ]
    
    print("SERP关键词分析器性能测试")
    print("=" * 50)
    print(f"测试关键词数量: {len(test_keywords)}")
    print(f"测试关键词: {', '.join(test_keywords)}")
    print("=" * 50)
    
    # 创建性能监控器
    monitor = PerformanceMonitor()
    
    try:
        # 运行基准测试
        results = await monitor.benchmark_multiple_keywords(test_keywords, delay=2.0)
        
        # 生成报告
        report = monitor.generate_report()
        
        # 打印摘要
        print("\n" + "=" * 50)
        print("性能测试摘要")
        print("=" * 50)
        
        summary = report['summary']
        print(f"总关键词数量: {summary['total_keywords']}")
        print(f"成功分析数量: {summary['successful_analyses']}")
        print(f"失败分析数量: {summary['failed_analyses']}")
        print(f"成功率: {summary['success_rate']}")
        print(f"总耗时: {summary['total_time']}")
        print(f"平均每个关键词耗时: {summary['average_time_per_keyword']}")
        print(f"平均搜索结果数量: {summary['average_search_results']}")
        print(f"估计API调用次数: {summary['estimated_api_calls']}")
        
        # 显示错误（如果有）
        if report['errors']:
            print(f"\n错误详情:")
            for error in report['errors']:
                print(f"- {error['keyword']}: {error['error']}")
                
        # 保存报告
        monitor.save_report(report)
        monitor.save_detailed_results(results)
        
        print(f"\n详细报告已保存:")
        print(f"- performance_report.json (性能摘要)")
        print(f"- benchmark_results.csv (详细结果)")
        
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"\n性能测试失败: {e}")


if __name__ == "__main__":
    asyncio.run(run_performance_test())
