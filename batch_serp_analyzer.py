#!/usr/bin/env python3
"""
批量SERP关键词分析器

优化版本：
1. 搜索语言设置为英文，搜索地区为全球
2. 每15个关键词调用一次LLM API，减少API调用成本
"""

import asyncio
import argparse
import sys
import time
import csv
import logging
from pathlib import Path
from typing import List, Dict, Any

from modules.batch_analyzer import BatchSERPAnalyzer

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('batch_serp_analyzer.log')
    ]
)
logger = logging.getLogger(__name__)


def load_keywords_from_csv(file_path: str, keyword_column: str = "Generated Keyword") -> List[str]:
    """
    从CSV文件加载关键词
    
    Args:
        file_path: CSV文件路径
        keyword_column: 关键词列名
        
    Returns:
        关键词列表
    """
    keywords = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            
            if keyword_column not in reader.fieldnames:
                raise ValueError(f"列 '{keyword_column}' 不存在于CSV文件中")
                
            for row in reader:
                keyword = row[keyword_column].strip()
                if keyword:
                    keywords.append(keyword)
                    
        logger.info(f"从 {file_path} 加载了 {len(keywords)} 个关键词")
        return keywords
        
    except Exception as e:
        logger.error(f"加载CSV文件失败: {e}")
        raise


def save_results_to_csv(results: List[Dict[str, Any]], output_path: str):
    """
    保存分析结果到CSV文件
    
    Args:
        results: 分析结果列表
        output_path: 输出文件路径
    """
    if not results:
        logger.warning("没有结果可保存")
        return
        
    try:
        with open(output_path, 'w', newline='', encoding='utf-8') as f:
            fieldnames = [
                'Primary_Keyword',
                'Merged_Keywords', 
                'Keyword_Count',
                'Search_Intent',
                'Content_Gaps',
                'Existing_Content_Types',
                'Confidence_Score',
                'Recommendations',
                'Search_Results_Count',
                'Analysis_Time',
                'Batch_Number',
                'Merged_From'
            ]
            
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            
            for result in results:
                # 安全地处理可能是字典或其他类型的字段
                def safe_join(value, default=''):
                    if isinstance(value, list):
                        return '; '.join([str(item) for item in value])
                    elif value:
                        return str(value)
                    else:
                        return default
                
                # 处理搜索意图字段
                search_intent = result.get('search_intent', '')
                if isinstance(search_intent, dict):
                    search_intent = str(search_intent.get('primary_intent', '')) + ' - ' + str(search_intent.get('details', ''))
                else:
                    search_intent = str(search_intent)
                
                row = {
                    'Primary_Keyword': result.get('primary_keyword', result.get('keyword', '')),
                    'Merged_Keywords': safe_join(result.get('merged_keywords', [result.get('keyword', '')])),
                    'Keyword_Count': result.get('keyword_count', 1),
                    'Search_Intent': search_intent,
                    'Content_Gaps': safe_join(result.get('content_gaps', [])),
                    'Existing_Content_Types': safe_join(result.get('existing_content_types', [])),
                    'Confidence_Score': result.get('confidence_score', 0),
                    'Recommendations': safe_join(result.get('recommendations', [])),
                    'Search_Results_Count': result.get('search_results_count', 0),
                    'Analysis_Time': result.get('total_analysis_time', result.get('batch_analysis_time', 0)),
                    'Batch_Number': result.get('batch_number', 1),
                    'Merged_From': result.get('merged_from', 1)
                }
                writer.writerow(row)
                
        logger.info(f"结果已保存到: {output_path}")
        
    except Exception as e:
        logger.error(f"保存结果失败: {e}")
        raise


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='批量SERP关键词分析器')
    parser.add_argument('--input', '-i', required=True, help='输入CSV文件路径')
    parser.add_argument('--output', '-o', required=True, help='输出CSV文件路径')
    parser.add_argument('--batch-size', '-b', type=int, default=15, help='批次大小（每批次关键词数量）')
    parser.add_argument('--max-keywords', '-m', type=int, help='最大处理关键词数量（用于测试）')
    parser.add_argument('--similarity', '-s', type=float, default=0.8, help='关键词相似度阈值')
    parser.add_argument('--delay', '-d', type=float, default=3.0, help='搜索间延迟时间（秒）')
    parser.add_argument('--keyword-column', '-k', default='Generated Keyword', help='关键词列名')
    
    args = parser.parse_args()
    
    # 验证输入文件
    if not Path(args.input).exists():
        logger.error(f"输入文件不存在: {args.input}")
        sys.exit(1)
        
    try:
        logger.info("=" * 60)
        logger.info("批量SERP关键词分析器启动")
        logger.info("=" * 60)
        logger.info(f"输入文件: {args.input}")
        logger.info(f"输出文件: {args.output}")
        logger.info(f"批次大小: {args.batch_size}")
        logger.info(f"相似度阈值: {args.similarity}")
        logger.info(f"搜索延迟: {args.delay}秒")
        
        # 加载关键词
        logger.info("正在加载关键词...")
        keywords = load_keywords_from_csv(args.input, args.keyword_column)
        
        # 限制关键词数量（如果指定）
        if args.max_keywords:
            keywords = keywords[:args.max_keywords]
            logger.info(f"限制处理关键词数量为: {args.max_keywords}")
            
        logger.info(f"将处理 {len(keywords)} 个关键词")
        
        # 初始化批量分析器
        analyzer = BatchSERPAnalyzer(
            batch_size=args.batch_size,
            similarity_threshold=args.similarity,
            delay_between_searches=args.delay
        )
        
        # 开始批量分析
        start_time = time.time()
        logger.info("开始批量分析...")
        
        results = await analyzer.analyze_keywords_batch(keywords)
        
        # 合并相似关键词
        logger.info("开始合并相似关键词...")
        merged_results = analyzer.merge_similar_keywords(results)
        
        total_time = time.time() - start_time
        
        # 保存结果
        logger.info("保存分析结果...")
        save_results_to_csv(merged_results, args.output)
        
        # 输出统计信息
        logger.info("=" * 60)
        logger.info("分析完成！")
        logger.info("=" * 60)
        logger.info(f"处理关键词数量: {len(keywords)}")
        logger.info(f"原始结果数量: {len(results)}")
        logger.info(f"合并后结果数量: {len(merged_results)}")
        logger.info(f"总耗时: {total_time:.2f} 秒")
        logger.info(f"平均每个关键词: {total_time/len(keywords):.2f} 秒")
        logger.info(f"输出文件: {args.output}")
        
        # 显示前几个结果示例
        if merged_results:
            logger.info("\n前3个分析结果示例:")
            for i, result in enumerate(merged_results[:3], 1):
                primary_keyword = result.get('primary_keyword', result.get('keyword', 'N/A'))
                merged_count = result.get('keyword_count', 1)
                confidence = result.get('confidence_score', 0)
                logger.info(f"{i}. {primary_keyword} (合并了{merged_count}个关键词, 置信度:{confidence:.2f})")
                
    except KeyboardInterrupt:
        logger.info("用户中断操作")
        sys.exit(1)
    except Exception as e:
        logger.error(f"分析失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
