# SERP关键词分析器

一个使用playwright爬取Google搜索结果，并使用Gemini 2.5 Flash分析搜索意图、内容缺口和已有内容形式的智能工具。支持批量处理CSV文件中的关键词，并能智能合并相似关键词。

## 功能特性

- 🔍 **Google搜索爬虫**: 使用playwright自动化爬取Google搜索结果
- 🤖 **AI智能分析**: 使用Gemini 2.5 Flash分析搜索意图和内容格局
- 📊 **批量处理**: 支持CSV文件批量处理关键词
- 🔄 **智能合并**: 自动识别和合并相似关键词
- 📝 **提示词管理**: 使用prompt_manager系统管理AI提示词
- ⚡ **异步处理**: 基于asyncio的高效异步处理

## 安装依赖

```bash
# 安装Python依赖
pip install playwright autogen-agentchat[all] google-generativeai pandas python-dotenv

# 安装playwright浏览器
playwright install chromium

# 安装系统依赖（Linux）
sudo apt-get install libnspr4 libnss3 libasound2t64
```

## 配置

1. 复制环境变量配置文件：
```bash
cp .env.example .env
```

2. 编辑`.env`文件，填入你的API密钥：
```bash
GEMINI_API_KEY=your_gemini_api_key_here
```

## 使用方法

### 1. 分析单个关键词

```bash
python serp_keyword_analyzer.py --keyword "python programming"
```

### 2. 批量处理CSV文件

```bash
python serp_keyword_analyzer.py --input input.csv --output output.csv
```

### 3. 运行测试

```bash
python serp_keyword_analyzer.py --test
```

### 4. 高级选项

```bash
# 指定关键词列名
python serp_keyword_analyzer.py --input input.csv --output output.csv --column "Generated Keyword"

# 设置相似度阈值
python serp_keyword_analyzer.py --input input.csv --output output.csv --similarity 0.8

# 设置关键词间延迟
python serp_keyword_analyzer.py --input input.csv --output output.csv --delay 5.0

# 限制处理数量（用于测试）
python serp_keyword_analyzer.py --input input.csv --output output.csv --max-keywords 10

# 详细输出
python serp_keyword_analyzer.py --input input.csv --output output.csv --verbose
```

## 输出格式

处理后的CSV文件包含以下字段：

- `Primary_Keyword`: 主关键词
- `Merged_Keywords`: 合并的关键词列表
- `Keyword_Count`: 合并的关键词数量
- `Search_Intent`: 用户搜索意图分析
- `Content_Gaps`: 识别的内容缺口
- `Existing_Content_Types`: 已有内容形式
- `Confidence_Score`: 分析置信度
- `Recommendations`: 内容创作建议
- `Search_Results_Count`: 搜索结果数量
- `Analysis_Time`: 分析耗时
- `Merged_From`: 合并来源数量

## 项目结构

```
serp_augment_agent/
├── serp_keyword_analyzer.py    # 主脚本
├── test_analyzer.py            # 测试脚本
├── modules/                    # 核心模块
│   ├── __init__.py
│   ├── google_scraper.py       # Google搜索爬虫
│   ├── gemini_analyzer.py      # Gemini分析智能体
│   ├── keyword_analyzer.py     # 关键词分析主函数
│   └── keyword_processor.py    # CSV处理和关键词合并
├── prompt_store/               # 提示词存储
│   └── serp_analyzer/
│       ├── config.json
│       ├── system_message.md
│       ├── user_message.md
│       └── analysis_guidelines.md
├── prompt_manager/             # 提示词管理系统
├── .env                        # 环境变量配置
├── .env.example               # 环境变量示例
└── README.md                  # 说明文档
```

## 快速开始

### 运行示例

```bash
# 运行完整的使用示例
python example_usage.py
```

### 测试系统

运行完整测试套件：

```bash
python test_analyzer.py
```

或者运行内置测试：

```bash
python serp_keyword_analyzer.py --test
```

### 处理真实数据

处理项目中的CSV文件（建议先测试少量数据）：

```bash
# 处理前10个关键词进行测试
python serp_keyword_analyzer.py --input reopentest_Global_English_Keyword_Report_deduplicated_corrected.csv --output results.csv --max-keywords 10

# 处理所有关键词（需要较长时间）
python serp_keyword_analyzer.py --input reopentest_Global_English_Keyword_Report_deduplicated_corrected.csv --output full_results.csv
```

## 注意事项

1. **API限制**: 请注意Gemini API的调用限制和配额
2. **爬虫礼貌**: 程序已内置延迟机制，请勿过于频繁地爬取
3. **网络环境**: 确保能够正常访问Google和Gemini API
4. **资源消耗**: 大批量处理时会消耗较多时间和API配额

## 故障排除

### 常见问题

1. **浏览器启动失败**
   - 确保已安装playwright浏览器：`playwright install chromium`
   - 安装系统依赖：`sudo apt-get install libnspr4 libnss3 libasound2t64`

2. **API调用失败**
   - 检查`.env`文件中的API密钥是否正确
   - 确认网络能够访问Gemini API

3. **CSV文件处理失败**
   - 检查CSV文件格式和编码
   - 确认指定的列名是否存在

### 日志文件

程序运行时会生成`serp_analyzer.log`日志文件，包含详细的执行信息。

## 技术架构

- **爬虫引擎**: Playwright (异步网页自动化)
- **AI框架**: AutoGen v0.4 (多智能体对话框架)
- **AI模型**: Gemini 2.5 Flash (Google生成式AI)
- **提示词管理**: 自定义prompt_manager系统
- **数据处理**: Pandas (CSV处理和数据分析)
- **异步处理**: asyncio (高效并发处理)

## 许可证

本项目遵循编码规则要求，严禁重复造轮子，使用现有成熟框架和工具。
