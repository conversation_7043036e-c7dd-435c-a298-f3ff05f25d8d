#!/usr/bin/env python3
"""
SERP关键词分析器主脚本

使用playwright爬取Google搜索结果，使用Gemini 2.5 Flash分析搜索意图、内容缺口和已有内容形式，
并对相似关键词进行智能合并。

使用方法:
    python serp_keyword_analyzer.py --input input.csv --output output.csv
    python serp_keyword_analyzer.py --keyword "python programming"
    python serp_keyword_analyzer.py --test
"""

import asyncio
import argparse
import json
import sys
import time
from pathlib import Path
from typing import List, Dict, Any
import logging

# 导入自定义模块
from modules import (
    analyze_single_keyword,
    process_csv_keywords,
    KeywordProcessor
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('serp_analyzer.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)


class SERPKeywordAnalyzer:
    """SERP关键词分析器主类"""
    
    def __init__(self):
        """初始化分析器"""
        self.prompt_store_path = "./prompt_store"
        
    async def analyze_single_keyword(self, keyword: str) -> Dict[str, Any]:
        """分析单个关键词"""
        try:
            logger.info(f"开始分析单个关键词: {keyword}")
            result = await analyze_single_keyword(keyword, self.prompt_store_path)
            
            # 打印结果
            self._print_single_result(result)
            return result
            
        except Exception as e:
            logger.error(f"分析关键词失败: {e}")
            raise
            
    async def process_csv_file(self, 
                             input_path: str, 
                             output_path: str,
                             keyword_column: str = "Generated Keyword",
                             similarity_threshold: float = 0.8,
                             delay_between_keywords: float = 5.0,
                             max_keywords: int = None) -> List[Dict[str, Any]]:
        """处理CSV文件"""
        try:
            logger.info(f"开始处理CSV文件: {input_path}")
            
            # 检查输入文件
            if not Path(input_path).exists():
                raise FileNotFoundError(f"输入文件不存在: {input_path}")
                
            # 如果指定了最大关键词数量，先加载并截取
            if max_keywords:
                processor = KeywordProcessor()
                all_keywords = processor.load_keywords_from_csv(input_path, keyword_column)
                keywords = all_keywords[:max_keywords]
                logger.info(f"限制处理前 {max_keywords} 个关键词（总共 {len(all_keywords)} 个）")
                
                # 创建临时处理器处理截取的关键词
                results = await processor.process_keywords(keywords, self.prompt_store_path)
                processor.save_results_to_csv(results, output_path)
            else:
                # 处理所有关键词
                results = await process_csv_keywords(
                    csv_path=input_path,
                    output_path=output_path,
                    keyword_column=keyword_column,
                    prompt_store_path=self.prompt_store_path,
                    similarity_threshold=similarity_threshold,
                    delay_between_keywords=delay_between_keywords
                )
            
            # 打印处理摘要
            self._print_processing_summary(results, input_path, output_path)
            return results
            
        except Exception as e:
            logger.error(f"处理CSV文件失败: {e}")
            raise
            
    async def run_test(self):
        """运行测试"""
        logger.info("开始运行测试...")
        
        # 测试关键词列表
        test_keywords = [
            "python programming",
            "machine learning",
            "web development"
        ]
        
        try:
            for keyword in test_keywords:
                logger.info(f"测试关键词: {keyword}")
                result = await self.analyze_single_keyword(keyword)
                print("\n" + "="*50 + "\n")
                
        except Exception as e:
            logger.error(f"测试失败: {e}")
            raise
            
    def _print_single_result(self, result: Dict[str, Any]):
        """打印单个关键词分析结果"""
        print("\n" + "="*60)
        print(f"关键词分析结果: {result['keyword']}")
        print("="*60)
        print(f"搜索意图: {result.get('search_intent', 'N/A')}")
        print(f"内容缺口: {result.get('content_gaps', [])}")
        print(f"已有内容形式: {result.get('existing_content_types', [])}")
        print(f"置信度: {result.get('confidence_score', 0):.2f}")
        print(f"建议: {result.get('recommendations', [])}")
        print(f"搜索结果数量: {result.get('search_results_count', 0)}")
        print(f"分析时间: {result.get('analysis_time', 0):.2f} 秒")
        
        if result.get('error'):
            print(f"错误信息: {result['error']}")
            
    def _print_processing_summary(self, results: List[Dict[str, Any]], input_path: str, output_path: str):
        """打印处理摘要"""
        total_keywords = sum(result.get('keyword_count', 1) for result in results)
        merged_groups = len(results)
        total_time = sum(result.get('analysis_time', result.get('total_analysis_time', 0)) for result in results)
        
        print("\n" + "="*60)
        print("处理摘要")
        print("="*60)
        print(f"输入文件: {input_path}")
        print(f"输出文件: {output_path}")
        print(f"原始关键词数量: {total_keywords}")
        print(f"合并后组数: {merged_groups}")
        print(f"合并率: {(1 - merged_groups/total_keywords)*100:.1f}%")
        print(f"总处理时间: {total_time:.2f} 秒")
        print(f"平均每个关键词: {total_time/total_keywords:.2f} 秒")
        
        # 显示前几个结果示例
        print("\n前3个分析结果示例:")
        for i, result in enumerate(results[:3], 1):
            primary_keyword = result.get('primary_keyword', result.get('keyword', 'N/A'))
            merged_count = result.get('keyword_count', 1)
            confidence = result.get('confidence_score', 0)
            print(f"{i}. {primary_keyword} (合并了{merged_count}个关键词, 置信度:{confidence:.2f})")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="SERP关键词分析器")
    
    # 互斥参数组
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument("--input", "-i", help="输入CSV文件路径")
    group.add_argument("--keyword", "-k", help="分析单个关键词")
    group.add_argument("--test", "-t", action="store_true", help="运行测试")
    
    # 可选参数
    parser.add_argument("--output", "-o", help="输出CSV文件路径（处理CSV时必需）")
    parser.add_argument("--column", "-c", default="Generated Keyword", help="关键词列名（默认: Generated Keyword）")
    parser.add_argument("--similarity", "-s", type=float, default=0.8, help="相似度阈值（默认: 0.8）")
    parser.add_argument("--delay", "-d", type=float, default=5.0, help="关键词间延迟秒数（默认: 5.0）")
    parser.add_argument("--max-keywords", "-m", type=int, help="最大处理关键词数量（用于测试）")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")
    
    args = parser.parse_args()
    
    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
        
    # 创建分析器
    analyzer = SERPKeywordAnalyzer()
    
    async def run():
        try:
            if args.test:
                # 运行测试
                await analyzer.run_test()
                
            elif args.keyword:
                # 分析单个关键词
                await analyzer.analyze_single_keyword(args.keyword)
                
            elif args.input:
                # 处理CSV文件
                if not args.output:
                    print("错误: 处理CSV文件时必须指定输出文件路径 (--output)")
                    sys.exit(1)
                    
                await analyzer.process_csv_file(
                    input_path=args.input,
                    output_path=args.output,
                    keyword_column=args.column,
                    similarity_threshold=args.similarity,
                    delay_between_keywords=args.delay,
                    max_keywords=args.max_keywords
                )
                
        except KeyboardInterrupt:
            logger.info("用户中断操作")
            sys.exit(0)
        except Exception as e:
            logger.error(f"程序执行失败: {e}")
            sys.exit(1)
            
    # 运行异步主函数
    asyncio.run(run())


if __name__ == "__main__":
    main()
