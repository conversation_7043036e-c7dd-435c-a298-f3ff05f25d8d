#!/usr/bin/env python3
"""
Keyword Spelling Corrector for Google Keyword Planner Data
识别并修正明显的拼写错误
"""

import csv
import re
import sys
from typing import Dict, List, Tuple

class KeywordSpellingCorrector:
    def __init__(self, enable_deduplication=True):
        self.enable_deduplication = enable_deduplication
        # 定义拼写错误映射表
        self.spelling_corrections = {
            # 药物名称错误
            'sparacetam': 'paracetamol',
            'stramal': 'tramadol',
            'stramadoal': 'tramadol',
            'sdrug tram': 'tramadol',
            'stramadol': 'tramadol',
            'tramadoale': 'tramadol',
            'tramal': 'tramadol',
            'tradol': 'tramadol',
            
            # Gabapentin相关错误
            'sgabapin': 'gabapentin',
            'sgabapenti': 'gabapentin',
            'sgabantin': 'gabapentin',
            'sdrug gaba': 'gabapentin',
            'gabapin': 'gabapentin',
            'gabantin': 'gabapentin',
            'biogabalin': 'pregabalin',  # 从CSV中看到的错误
            
            # 苯二氮卓类药物错误
            'sclonazepa': 'clonazepam',
            'salprazola': 'alprazolam',
            'xana x': 'xanax',
            'xanaxs': 'xanax',
            'valiums': 'valium',
            'klonopins': 'klonopin',
            
            # 维生素错误
            'svetamin c': 'vitamin c',
            'vetamin d': 'vitamin d',
            'vitamin v12': 'vitamin b12',
            'vitd': 'vitamin d',
            'vita d3': 'vitamin d3',
            'vit d3': 'vitamin d3',
            'vt d deficiency': 'vitamin d deficiency',
            'witamin d': 'vitamin d',
            'vitaminb5': 'vitamin b5',
            
            # 疾病和病毒错误
            'scovid 19': 'covid-19',
            'covid 19': 'covid-19',
            'carcinoem': 'carcinoma',
            'shmpv': 'hmpv',
            'hmpv virus': 'hmpv',
            'adenoviirus': 'adenovirus',
            'cytomeg': 'cytomegalovirus',
            'mikoplazma': 'mycoplasma',
            'hepatit c': 'hepatitis c',
            'epps barr': 'epstein barr',
            'ep barr': 'epstein barr',
            'e barr': 'epstein barr',
            'barre epstein': 'epstein barr',
            'bar epstein': 'epstein barr',
            
            # 其他医学术语错误
            'bacteriosis vaginalis': 'bacterial vaginosis',
            'grandella vaginalis': 'gardnerella vaginalis',
            'gardenella': 'gardnerella',
            'gardnerellavaginalis': 'gardnerella vaginalis',
            'gardnerella vajinalis': 'gardnerella vaginalis',
            'gardnerella vaginlis': 'gardnerella vaginalis',
            'gardnerella vaginalid': 'gardnerella vaginalis',
            'gardenella varginalis': 'gardnerella vaginalis',
            'gardenella vaginilis': 'gardnerella vaginalis',
            'gardenella vaginallis': 'gardnerella vaginalis',
            'gard vaginalis': 'gardnerella vaginalis',
            'g vaginalis': 'gardnerella vaginalis',
            
            # 药物剂量和形式错误
            'ritali n': 'ritalin',
            'methylphe': 'methylphenidate',
            'methylpheni': 'methylphenidate',
            'dextrome': 'dextromethorphan',
            'dilaudide': 'dilaudid',
            'oxy contin': 'oxycontin',
            
            # 检测相关错误
            'ddimertest': 'd-dimer test',
            'didimar test': 'd-dimer test',
            'di mer': 'd-dimer',
            'ckmb test': 'ck-mb test',
            'cpk mb': 'ck-mb',
            'cpk ck mb': 'ck-mb',
            'ck ck mb': 'ck-mb',
            'ck and ck mb': 'ck-mb',
            
            # 症状描述错误
            'flu bsymptoms': 'flu symptoms',
            'how long doe sthe flu last': 'how long does the flu last',
            'how long does a the flu last': 'how long does the flu last',
            'how long does the a flu last': 'how long does the flu last',
            
            # 其他常见错误
            'proteinec reactive': 'c reactive protein',
            'streptokoka agalactiae': 'streptococcus agalactiae',
            'haemophilus vaginalis': 'gardnerella vaginalis',
            'mullerian hormon': 'mullerian hormone',
            'anti m��llerian': 'anti mullerian',
            'betakaroten': 'beta carotene',
        }
        
        # 编译正则表达式模式以提高性能
        self.compiled_patterns = {}
        for wrong, correct in self.spelling_corrections.items():
            # 创建不区分大小写的模式，但保持单词边界
            pattern = r'\b' + re.escape(wrong) + r'\b'
            self.compiled_patterns[pattern] = correct
    
    def correct_keyword(self, keyword: str) -> Tuple[str, bool]:
        """
        修正单个关键词的拼写错误
        返回: (修正后的关键词, 是否有修改)
        """
        if not isinstance(keyword, str):
            return keyword, False
            
        original_keyword = keyword
        corrected_keyword = keyword.lower()
        
        # 应用拼写修正
        for pattern, correction in self.compiled_patterns.items():
            corrected_keyword = re.sub(pattern, correction, corrected_keyword, flags=re.IGNORECASE)
        
        # 检查是否有修改
        has_changes = corrected_keyword != original_keyword.lower()
        
        return corrected_keyword, has_changes

    def deduplicate_keywords(self, rows: List[List[str]], keyword_column_index: int) -> Tuple[List[List[str]], Dict]:
        """
        去重关键词，保留第一次出现的条目
        返回: (去重后的行列表, 去重统计信息)
        """
        seen_keywords = set()
        deduplicated_rows = []
        duplicate_info = []

        # 保留标题行
        if rows:
            deduplicated_rows.append(rows[0])

        # 处理数据行
        for idx, row in enumerate(rows[1:], start=1):
            if keyword_column_index < len(row):
                keyword = row[keyword_column_index].lower().strip()

                if keyword not in seen_keywords:
                    seen_keywords.add(keyword)
                    deduplicated_rows.append(row)
                else:
                    duplicate_info.append({
                        'row': idx + 1,  # +1 因为CSV行号从1开始
                        'keyword': row[keyword_column_index],
                        'original_keyword': keyword
                    })
            else:
                # 如果行数据不完整，仍然保留
                deduplicated_rows.append(row)

        dedup_stats = {
            'original_count': len(rows) - 1,  # 减去标题行
            'deduplicated_count': len(deduplicated_rows) - 1,  # 减去标题行
            'duplicates_removed': len(duplicate_info),
            'duplicate_details': duplicate_info
        }

        return deduplicated_rows, dedup_stats

    def process_csv(self, input_file: str, output_file: str = None) -> Dict:
        """
        处理CSV文件，修正拼写错误
        """
        try:
            # 读取CSV文件，尝试不同的编码
            rows = []
            keyword_column_index = None

            # 尝试不同的编码
            encodings = ['iso-8859-1', 'latin-1', 'cp1252', 'utf-8']
            csvfile = None
            used_encoding = None

            for encoding in encodings:
                try:
                    csvfile = open(input_file, 'r', encoding=encoding, newline='')
                    # 尝试读取第一行来验证编码
                    pos = csvfile.tell()
                    csvfile.readline()
                    csvfile.seek(pos)
                    used_encoding = encoding
                    break
                except (UnicodeDecodeError, UnicodeError):
                    if csvfile:
                        csvfile.close()
                    continue

            if csvfile is None:
                raise ValueError("无法使用任何编码读取文件")

            print(f"使用编码: {used_encoding}")

            with csvfile:
                reader = csv.reader(csvfile)
                header = next(reader)

                # 查找Generated Keyword列的索引
                try:
                    keyword_column_index = header.index('Generated Keyword')
                except ValueError:
                    raise ValueError("CSV文件中未找到'Generated Keyword'列")

                rows.append(header)

                # 读取所有数据行
                for row in reader:
                    rows.append(row)

            # 统计信息
            stats = {
                'total_keywords': len(rows) - 1,  # 减去标题行
                'corrected_keywords': 0,
                'corrections': [],
                'deduplication': None
            }

            # 处理每个关键词（跳过标题行）
            for idx in range(1, len(rows)):
                if keyword_column_index < len(rows[idx]):
                    original_keyword = rows[idx][keyword_column_index]
                    corrected_keyword, has_changes = self.correct_keyword(original_keyword)

                    if has_changes:
                        rows[idx][keyword_column_index] = corrected_keyword
                        stats['corrected_keywords'] += 1
                        stats['corrections'].append({
                            'row': idx + 1,  # +1 因为CSV行号从1开始
                            'original': original_keyword,
                            'corrected': corrected_keyword
                        })

            # 执行去重（如果启用）
            if self.enable_deduplication:
                print("正在执行去重...")
                rows, dedup_stats = self.deduplicate_keywords(rows, keyword_column_index)
                stats['deduplication'] = dedup_stats
                print(f"去重完成: 移除了 {dedup_stats['duplicates_removed']} 个重复项")

            # 保存修正后的文件
            if output_file is None:
                output_file = input_file.replace('.csv', '_corrected.csv')

            with open(output_file, 'w', encoding='utf-8', newline='') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerows(rows)

            return {
                'success': True,
                'output_file': output_file,
                'stats': stats
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def print_corrections_report(self, stats: Dict):
        """
        打印修正报告
        """
        print(f"\n=== 拼写错误修正和去重报告 ===")
        print(f"原始关键词数量: {stats['total_keywords']}")
        print(f"修正的关键词数量: {stats['corrected_keywords']}")
        print(f"修正率: {stats['corrected_keywords']/stats['total_keywords']*100:.2f}%")

        # 去重信息
        if stats.get('deduplication'):
            dedup = stats['deduplication']
            print(f"\n=== 去重统计 ===")
            print(f"去重前数量: {dedup['original_count']}")
            print(f"去重后数量: {dedup['deduplicated_count']}")
            print(f"移除重复项: {dedup['duplicates_removed']}")
            print(f"去重率: {dedup['duplicates_removed']/dedup['original_count']*100:.2f}%")

        if stats['corrections']:
            print(f"\n=== 拼写修正详情 ===")
            for correction in stats['corrections'][:20]:  # 只显示前20个修正
                print(f"  行 {correction['row']}: '{correction['original']}' -> '{correction['corrected']}'")

            if len(stats['corrections']) > 20:
                print(f"  ... 还有 {len(stats['corrections']) - 20} 个修正")

        # 重复项详情（只显示前10个）
        if stats.get('deduplication') and stats['deduplication']['duplicate_details']:
            print(f"\n=== 重复项详情（前10个）===")
            for i, dup in enumerate(stats['deduplication']['duplicate_details'][:10]):
                print(f"  行 {dup['row']}: '{dup['keyword']}' (重复)")

            if len(stats['deduplication']['duplicate_details']) > 10:
                remaining = len(stats['deduplication']['duplicate_details']) - 10
                print(f"  ... 还有 {remaining} 个重复项")

def main():
    """
    主函数
    """
    if len(sys.argv) < 2:
        print("使用方法: python keyword_spelling_corrector.py <input_csv_file> [output_csv_file] [--no-dedup]")
        print("参数说明:")
        print("  input_csv_file: 输入的CSV文件路径")
        print("  output_csv_file: 输出的CSV文件路径（可选）")
        print("  --no-dedup: 禁用去重功能（可选）")
        print("示例: python keyword_spelling_corrector.py input.csv output.csv")
        print("示例: python keyword_spelling_corrector.py input.csv --no-dedup")
        sys.exit(1)

    input_file = sys.argv[1]
    output_file = None
    enable_deduplication = True

    # 解析命令行参数
    for i in range(2, len(sys.argv)):
        arg = sys.argv[i]
        if arg == '--no-dedup':
            enable_deduplication = False
        elif not arg.startswith('--'):
            output_file = arg

    # 创建修正器实例
    corrector = KeywordSpellingCorrector(enable_deduplication=enable_deduplication)

    # 处理文件
    print(f"正在处理文件: {input_file}")
    if enable_deduplication:
        print("启用去重功能")
    else:
        print("禁用去重功能")

    result = corrector.process_csv(input_file, output_file)

    if result['success']:
        print(f"✅ 处理完成! 输出文件: {result['output_file']}")
        corrector.print_corrections_report(result['stats'])
    else:
        print(f"❌ 处理失败: {result['error']}")
        sys.exit(1)

if __name__ == "__main__":
    main()
