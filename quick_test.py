#!/usr/bin/env python3
"""
快速测试脚本

验证SERP关键词分析器的核心功能是否正常工作。
"""

import asyncio
import sys
import time


async def test_google_scraper():
    """测试Google搜索爬虫"""
    print("🔍 测试Google搜索爬虫...")
    
    try:
        from modules.google_scraper import search_keyword
        
        results = await search_keyword("AI technology", 5)
        
        if results:
            print(f"✅ 成功获取 {len(results)} 个搜索结果")
            print(f"   示例结果: {results[0]['title'][:50]}...")
            return True
        else:
            print("❌ 未获取到搜索结果")
            return False
            
    except Exception as e:
        print(f"❌ Google搜索爬虫测试失败: {e}")
        return False


async def test_gemini_analyzer():
    """测试Gemini分析器"""
    print("🤖 测试Gemini分析器...")
    
    try:
        from modules.gemini_analyzer import analyze_keyword_serp
        
        # 模拟搜索结果
        mock_results = [
            {
                'title': 'What is Artificial Intelligence?',
                'url': 'https://example.com/ai-guide',
                'description': 'A comprehensive guide to understanding artificial intelligence and its applications.'
            },
            {
                'title': 'AI Technology Trends 2024',
                'url': 'https://example.com/ai-trends',
                'description': 'Latest trends and developments in AI technology for 2024.'
            }
        ]
        
        result = await analyze_keyword_serp("AI technology", mock_results)
        
        if result and result.get('search_intent'):
            print("✅ Gemini分析器工作正常")
            print(f"   置信度: {result.get('confidence_score', 0)}")
            return True
        else:
            print("❌ Gemini分析器返回结果异常")
            return False
            
    except Exception as e:
        print(f"❌ Gemini分析器测试失败: {e}")
        return False


async def test_full_pipeline():
    """测试完整流程"""
    print("🔄 测试完整分析流程...")
    
    try:
        from modules.keyword_analyzer import analyze_single_keyword
        
        start_time = time.time()
        result = await analyze_single_keyword("digital marketing")
        analysis_time = time.time() - start_time
        
        if result and result.get('keyword'):
            print("✅ 完整流程测试成功")
            print(f"   关键词: {result['keyword']}")
            print(f"   搜索结果数: {result.get('search_results_count', 0)}")
            print(f"   分析时间: {analysis_time:.2f}秒")
            print(f"   置信度: {result.get('confidence_score', 0)}")
            return True
        else:
            print("❌ 完整流程测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 完整流程测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 SERP关键词分析器快速测试")
    print("=" * 50)
    
    tests = [
        ("Google搜索爬虫", test_google_scraper),
        ("Gemini分析器", test_gemini_analyzer),
        ("完整分析流程", test_full_pipeline)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        try:
            success = await test_func()
            if success:
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            
        print()
    
    print("=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统可以正常使用。")
        print("\n📝 接下来您可以:")
        print("   1. 运行单个关键词分析:")
        print("      python serp_keyword_analyzer.py --keyword 'your keyword'")
        print("   2. 处理CSV文件:")
        print("      python serp_keyword_analyzer.py --input input.csv --output output.csv")
        print("   3. 运行完整示例:")
        print("      python example_usage.py")
        return True
    else:
        print("⚠️ 部分测试失败，请检查配置和依赖。")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
