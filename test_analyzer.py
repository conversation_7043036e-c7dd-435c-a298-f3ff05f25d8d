#!/usr/bin/env python3
"""
SERP关键词分析器测试脚本

用于测试各个模块的功能是否正常。
"""

import asyncio
import sys
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_google_scraper():
    """测试Google搜索爬虫"""
    print("\n=== 测试Google搜索爬虫 ===")
    
    try:
        from modules.google_scraper import search_keyword
        
        # 测试搜索
        results = await search_keyword("python programming", 5)
        
        print(f"成功获取 {len(results)} 个搜索结果:")
        for i, result in enumerate(results, 1):
            print(f"{i}. {result['title']}")
            print(f"   URL: {result['url']}")
            print(f"   描述: {result['description'][:100]}...")
            print()
            
        return True
        
    except Exception as e:
        print(f"Google搜索爬虫测试失败: {e}")
        return False


async def test_prompt_manager():
    """测试提示词管理器"""
    print("\n=== 测试提示词管理器 ===")
    
    try:
        from prompt_manager import PromptManager
        
        # 初始化提示词管理器
        pm = PromptManager("./prompt_store")
        
        # 获取SERP分析器的提示词
        prompts = pm.get_prompts('serp_analyzer')
        
        print("成功加载提示词:")
        print(f"- 系统提示词长度: {len(prompts['system'].template)} 字符")
        print(f"- 用户提示词长度: {len(prompts['user'].template)} 字符")
        print(f"- 系统提示词变量: {prompts['system'].input_variables}")
        print(f"- 用户提示词变量: {prompts['user'].input_variables}")
        
        return True
        
    except Exception as e:
        print(f"提示词管理器测试失败: {e}")
        return False


async def test_gemini_analyzer():
    """测试Gemini分析器"""
    print("\n=== 测试Gemini分析器 ===")
    
    try:
        from modules.gemini_analyzer import analyze_keyword_serp
        
        # 模拟搜索结果
        mock_results = [
            {
                'title': 'Python Programming Tutorial - Learn Python',
                'url': 'https://example.com/python-tutorial',
                'description': 'Complete Python programming tutorial for beginners. Learn Python syntax, data structures, and more.'
            },
            {
                'title': 'Python.org - Official Python Website',
                'url': 'https://python.org',
                'description': 'The official home of the Python Programming Language.'
            }
        ]
        
        # 测试分析
        result = await analyze_keyword_serp("python programming", mock_results)
        
        print("成功完成SERP分析:")
        print(f"- 关键词: {result.get('keyword', 'N/A')}")
        print(f"- 搜索意图: {str(result.get('search_intent', 'N/A'))[:100]}...")
        print(f"- 置信度: {result.get('confidence_score', 0)}")
        
        return True
        
    except Exception as e:
        print(f"Gemini分析器测试失败: {e}")
        return False


async def test_keyword_analyzer():
    """测试关键词分析器"""
    print("\n=== 测试关键词分析器 ===")
    
    try:
        from modules.keyword_analyzer import analyze_single_keyword
        
        # 测试分析单个关键词
        result = await analyze_single_keyword("python programming")
        
        print("成功完成关键词分析:")
        print(f"- 关键词: {result['keyword']}")
        print(f"- 搜索结果数量: {result.get('search_results_count', 0)}")
        print(f"- 分析时间: {result.get('analysis_time', 0):.2f} 秒")
        print(f"- 置信度: {result.get('confidence_score', 0)}")
        
        return True
        
    except Exception as e:
        print(f"关键词分析器测试失败: {e}")
        return False


async def test_csv_processing():
    """测试CSV处理功能"""
    print("\n=== 测试CSV处理功能 ===")
    
    try:
        # 检查CSV文件是否存在
        csv_file = "reopentest_Global_English_Keyword_Report_deduplicated_corrected.csv"
        if not Path(csv_file).exists():
            print(f"CSV文件不存在: {csv_file}")
            return False
            
        from modules.keyword_processor import KeywordProcessor
        
        # 测试加载关键词
        processor = KeywordProcessor()
        keywords = processor.load_keywords_from_csv(csv_file, "Generated Keyword")
        
        print(f"成功从CSV加载 {len(keywords)} 个关键词")
        print(f"前5个关键词: {keywords[:5]}")
        
        return True
        
    except Exception as e:
        print(f"CSV处理测试失败: {e}")
        return False


async def run_all_tests():
    """运行所有测试"""
    print("开始运行SERP关键词分析器测试...")
    
    tests = [
        ("提示词管理器", test_prompt_manager),
        ("Google搜索爬虫", test_google_scraper),
        ("Gemini分析器", test_gemini_analyzer),
        ("关键词分析器", test_keyword_analyzer),
        ("CSV处理功能", test_csv_processing),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*60}")
            print(f"正在测试: {test_name}")
            print('='*60)
            
            success = await test_func()
            results[test_name] = success
            
            if success:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
                
        except Exception as e:
            print(f"❌ {test_name} 测试出错: {e}")
            results[test_name] = False
            
    # 打印测试摘要
    print(f"\n{'='*60}")
    print("测试摘要")
    print('='*60)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, success in results.items():
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name}: {status}")
        
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！系统可以正常使用。")
        return True
    else:
        print("⚠️  部分测试失败，请检查配置和依赖。")
        return False


if __name__ == "__main__":
    success = asyncio.run(run_all_tests())
    sys.exit(0 if success else 1)
