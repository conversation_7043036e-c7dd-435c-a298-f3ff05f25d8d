#!/usr/bin/env python3
"""
SERP关键词分析器使用示例

演示如何使用SERP关键词分析器的各种功能。
"""

import asyncio
import sys
from modules import analyze_single_keyword, process_csv_keywords


async def example_single_keyword():
    """示例：分析单个关键词"""
    print("=== 单个关键词分析示例 ===")
    
    keyword = "machine learning"
    print(f"正在分析关键词: {keyword}")
    
    try:
        result = await analyze_single_keyword(keyword)
        
        print(f"\n关键词: {result['keyword']}")
        print(f"搜索结果数量: {result.get('search_results_count', 0)}")
        print(f"分析时间: {result.get('analysis_time', 0):.2f} 秒")
        print(f"置信度: {result.get('confidence_score', 0):.2f}")
        print(f"搜索意图: {str(result.get('search_intent', 'N/A'))[:200]}...")
        print(f"内容缺口: {result.get('content_gaps', [])[:3]}...")  # 只显示前3个
        print(f"已有内容类型: {result.get('existing_content_types', [])[:3]}...")
        print(f"建议: {result.get('recommendations', [])[:2]}...")  # 只显示前2个
        
    except Exception as e:
        print(f"分析失败: {e}")


async def example_csv_processing():
    """示例：处理CSV文件（少量关键词）"""
    print("\n=== CSV文件处理示例 ===")
    
    # 创建一个小的测试CSV文件
    test_csv_content = """Generated Keyword
python programming
machine learning
web development
data science
artificial intelligence"""
    
    with open("test_keywords.csv", "w", encoding="utf-8") as f:
        f.write(test_csv_content)
    
    print("创建了测试CSV文件: test_keywords.csv")
    print("正在处理前3个关键词...")
    
    try:
        # 处理CSV文件，只处理前3个关键词
        from modules.keyword_processor import KeywordProcessor
        
        processor = KeywordProcessor(
            similarity_threshold=0.8,
            delay_between_keywords=3.0
        )
        
        # 加载关键词
        keywords = processor.load_keywords_from_csv("test_keywords.csv", "Generated Keyword")
        keywords = keywords[:3]  # 只处理前3个
        
        # 处理关键词
        results = await processor.process_keywords(keywords)
        
        # 保存结果
        processor.save_results_to_csv(results, "example_output.csv")
        
        print(f"\n处理完成！")
        print(f"原始关键词数量: {len(keywords)}")
        print(f"合并后结果数量: {len(results)}")
        print(f"输出文件: example_output.csv")
        
        # 显示结果摘要
        for i, result in enumerate(results, 1):
            primary_keyword = result.get('primary_keyword', result.get('keyword', 'N/A'))
            merged_count = result.get('keyword_count', 1)
            confidence = result.get('confidence_score', 0)
            print(f"{i}. {primary_keyword} (合并了{merged_count}个关键词, 置信度:{confidence:.2f})")
            
    except Exception as e:
        print(f"CSV处理失败: {e}")


async def example_comparison():
    """示例：比较相似关键词"""
    print("\n=== 相似关键词比较示例 ===")
    
    keywords = ["python programming", "python coding", "learn python"]
    
    print(f"正在分析相似关键词: {keywords}")
    
    try:
        from modules.keyword_processor import KeywordProcessor
        
        processor = KeywordProcessor(
            similarity_threshold=0.7,  # 降低阈值以便合并
            delay_between_keywords=2.0
        )
        
        results = await processor.process_keywords(keywords)
        
        print(f"\n分析结果:")
        for result in results:
            primary_keyword = result.get('primary_keyword', result.get('keyword', 'N/A'))
            merged_keywords = result.get('merged_keywords', [])
            merged_count = result.get('keyword_count', 1)
            
            print(f"\n主关键词: {primary_keyword}")
            print(f"合并的关键词: {merged_keywords}")
            print(f"合并数量: {merged_count}")
            
            if merged_count > 1:
                print("✅ 检测到相似关键词并成功合并")
            else:
                print("ℹ️  未发现相似关键词")
                
    except Exception as e:
        print(f"比较分析失败: {e}")


async def main():
    """主函数"""
    print("SERP关键词分析器使用示例")
    print("=" * 50)
    
    try:
        # 示例1: 单个关键词分析
        await example_single_keyword()
        
        # 示例2: CSV文件处理
        await example_csv_processing()
        
        # 示例3: 相似关键词比较
        await example_comparison()
        
        print("\n" + "=" * 50)
        print("所有示例执行完成！")
        print("\n生成的文件:")
        print("- test_keywords.csv (测试输入文件)")
        print("- example_output.csv (示例输出文件)")
        print("\n您可以查看这些文件了解输出格式。")
        
    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"\n示例执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    # 运行示例
    asyncio.run(main())
