#!/usr/bin/env python3
"""
测试批量分析器
"""

import asyncio
import logging
from modules.batch_analyzer import BatchSERPAnalyzer

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_batch_analyzer():
    """测试批量分析器"""
    
    # 测试关键词
    test_keywords = [
        "artificial intelligence",
        "machine learning", 
        "data science",
        "python programming",
        "web development"
    ]
    
    logger.info(f"测试批量分析器，关键词: {test_keywords}")
    
    # 创建分析器
    analyzer = BatchSERPAnalyzer(
        batch_size=5,
        similarity_threshold=0.8,
        delay_between_searches=3.0
    )
    
    try:
        # 执行批量分析
        results = await analyzer.analyze_keywords_batch(test_keywords)
        
        logger.info(f"分析完成，获得 {len(results)} 个结果")
        
        # 显示结果
        for i, result in enumerate(results, 1):
            keyword = result.get('keyword', 'N/A')
            search_count = result.get('search_results_count', 0)
            confidence = result.get('confidence_score', 0)
            
            logger.info(f"{i}. {keyword}: {search_count} 个搜索结果, 置信度: {confidence}")
            
        # 测试合并功能
        merged_results = analyzer.merge_similar_keywords(results)
        logger.info(f"合并后: {len(merged_results)} 个结果")
        
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(test_batch_analyzer())
    if success:
        print("✅ 批量分析器测试成功")
    else:
        print("❌ 批量分析器测试失败")
