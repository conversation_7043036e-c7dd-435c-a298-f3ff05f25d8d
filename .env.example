# API Keys Configuration
# Copy this file to .env and fill in your actual API keys

# Google Gemini API Key
# Get from: https://makersuite.google.com/app/apikey
GEMINI_API_KEY=your_gemini_api_key_here

# Optional: OpenAI API Key (if needed for autogen)
OPENAI_API_KEY=your_openai_api_key_here

# Optional: Azure OpenAI Configuration
AZURE_OPENAI_API_KEY=your_azure_openai_api_key_here
AZURE_OPENAI_ENDPOINT=your_azure_openai_endpoint_here

# Scraping Configuration
SCRAPING_DELAY=2  # Delay between requests in seconds
MAX_RETRIES=3     # Maximum retry attempts for failed requests
