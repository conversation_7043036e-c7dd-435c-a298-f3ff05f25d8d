#!/usr/bin/env python3
"""
批量SERP分析模块

优化版本：每15个关键词调用一次LLM API，减少API调用成本。
"""

import asyncio
import time
import logging
from typing import List, Dict, Any
from difflib import SequenceMatcher

from .google_scraper import search_keyword
from .gemini_analyzer import analyze_keywords_batch

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class BatchSERPAnalyzer:
    """批量SERP分析器"""
    
    def __init__(self, 
                 batch_size: int = 15,
                 similarity_threshold: float = 0.8,
                 delay_between_searches: float = 3.0):
        """
        初始化批量分析器
        
        Args:
            batch_size: 每批次关键词数量（调用一次LLM）
            similarity_threshold: 关键词相似度阈值
            delay_between_searches: 搜索间隔时间
        """
        self.batch_size = batch_size
        self.similarity_threshold = similarity_threshold
        self.delay_between_searches = delay_between_searches
        
    async def analyze_keywords_batch(self, keywords: List[str]) -> List[Dict[str, Any]]:
        """
        批量分析关键词
        
        Args:
            keywords: 关键词列表
            
        Returns:
            分析结果列表
        """
        logger.info(f"开始批量分析 {len(keywords)} 个关键词，批次大小: {self.batch_size}")
        
        all_results = []
        
        # 按批次处理关键词
        for i in range(0, len(keywords), self.batch_size):
            batch_keywords = keywords[i:i + self.batch_size]
            batch_num = i // self.batch_size + 1
            total_batches = (len(keywords) + self.batch_size - 1) // self.batch_size
            
            logger.info(f"处理批次 {batch_num}/{total_batches}: {len(batch_keywords)} 个关键词")
            
            try:
                # 处理当前批次
                batch_results = await self._process_batch(batch_keywords, batch_num)
                all_results.extend(batch_results)
                
                logger.info(f"批次 {batch_num} 完成，获得 {len(batch_results)} 个结果")
                
                # 批次间延迟
                if i + self.batch_size < len(keywords):
                    logger.info(f"等待 {self.delay_between_searches} 秒后处理下一批次...")
                    await asyncio.sleep(self.delay_between_searches)
                    
            except Exception as e:
                logger.error(f"批次 {batch_num} 处理失败: {e}")
                # 继续处理下一批次
                continue
                
        logger.info(f"批量分析完成，总共获得 {len(all_results)} 个结果")
        return all_results
        
    async def _process_batch(self, keywords: List[str], batch_num: int) -> List[Dict[str, Any]]:
        """
        处理单个批次
        
        Args:
            keywords: 当前批次的关键词列表
            batch_num: 批次编号
            
        Returns:
            当前批次的分析结果
        """
        batch_start_time = time.time()
        
        # 步骤1: 收集所有关键词的搜索结果
        logger.info(f"批次 {batch_num}: 开始收集搜索结果...")
        keyword_search_data = []
        
        for i, keyword in enumerate(keywords, 1):
            try:
                logger.info(f"批次 {batch_num}: 搜索关键词 {i}/{len(keywords)}: {keyword}")
                
                search_start_time = time.time()

                # 重试机制
                max_retries = 3
                search_results = []

                for retry in range(max_retries):
                    try:
                        search_results = await search_keyword(keyword, max_results=15)
                        break  # 成功则跳出重试循环
                    except Exception as search_error:
                        logger.warning(f"关键词 '{keyword}' 搜索失败 (尝试 {retry + 1}/{max_retries}): {search_error}")
                        if retry < max_retries - 1:
                            # 增加延迟时间
                            retry_delay = (retry + 1) * 5
                            logger.info(f"等待 {retry_delay} 秒后重试...")
                            await asyncio.sleep(retry_delay)
                        else:
                            # 最后一次重试失败，记录错误
                            raise search_error

                search_time = time.time() - search_start_time
                
                keyword_search_data.append({
                    'keyword': keyword,
                    'search_results': search_results,
                    'search_results_count': len(search_results),
                    'search_time': search_time
                })
                
                logger.info(f"关键词 '{keyword}' 搜索完成，获得 {len(search_results)} 个结果，耗时 {search_time:.2f}s")
                
                # 搜索间延迟（增加延迟时间以避免反爬）
                if i < len(keywords):
                    delay_time = self.delay_between_searches + (i * 0.5)  # 逐渐增加延迟
                    logger.info(f"等待 {delay_time:.1f} 秒后搜索下一个关键词...")
                    await asyncio.sleep(delay_time)
                    
            except Exception as e:
                logger.error(f"关键词 '{keyword}' 搜索失败: {e}")
                # 添加空结果，继续处理
                keyword_search_data.append({
                    'keyword': keyword,
                    'search_results': [],
                    'search_results_count': 0,
                    'search_time': 0,
                    'error': str(e)
                })
                
        # 步骤2: 批量调用LLM分析
        logger.info(f"批次 {batch_num}: 开始LLM批量分析...")
        
        try:
            llm_start_time = time.time()
            llm_results = await analyze_keywords_batch(keyword_search_data)
            llm_time = time.time() - llm_start_time
            
            logger.info(f"批次 {batch_num}: LLM分析完成，耗时 {llm_time:.2f}s")
            
        except Exception as e:
            logger.error(f"批次 {batch_num}: LLM分析失败: {e}")
            # 如果LLM分析失败，返回基础搜索结果
            llm_results = {}
            
        # 步骤3: 合并结果
        batch_results = []
        for data in keyword_search_data:
            keyword = data['keyword']
            
            # 获取LLM分析结果
            llm_result = llm_results.get(keyword, {})
            
            # 合并结果
            result = {
                'keyword': keyword,
                'search_results_count': data['search_results_count'],
                'search_time': data['search_time'],
                'search_intent': llm_result.get('search_intent', ''),
                'content_gaps': llm_result.get('content_gaps', []),
                'existing_content_types': llm_result.get('existing_content_types', []),
                'confidence_score': llm_result.get('confidence_score', 0),
                'recommendations': llm_result.get('recommendations', []),
                'batch_number': batch_num,
                'batch_analysis_time': time.time() - batch_start_time
            }
            
            # 添加错误信息（如果有）
            if 'error' in data:
                result['search_error'] = data['error']
                
            batch_results.append(result)
            
        return batch_results
        
    def merge_similar_keywords(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        合并相似关键词的结果
        
        Args:
            results: 分析结果列表
            
        Returns:
            合并后的结果列表
        """
        logger.info(f"开始合并相似关键词，阈值: {self.similarity_threshold}")
        
        merged_results = []
        processed_indices = set()
        
        for i, result in enumerate(results):
            if i in processed_indices:
                continue
                
            # 查找相似关键词
            similar_indices = [i]
            base_keyword = result['keyword']
            
            for j, other_result in enumerate(results[i+1:], i+1):
                if j in processed_indices:
                    continue
                    
                similarity = SequenceMatcher(None, base_keyword.lower(), 
                                           other_result['keyword'].lower()).ratio()
                
                if similarity >= self.similarity_threshold:
                    similar_indices.append(j)
                    
            # 合并相似关键词的结果
            if len(similar_indices) > 1:
                merged_result = self._merge_keyword_results([results[idx] for idx in similar_indices])
                merged_results.append(merged_result)
                logger.info(f"合并了 {len(similar_indices)} 个相似关键词: {[results[idx]['keyword'] for idx in similar_indices]}")
            else:
                merged_results.append(result)
                
            # 标记已处理的索引
            processed_indices.update(similar_indices)
            
        logger.info(f"合并完成: {len(results)} -> {len(merged_results)} 个结果")
        return merged_results
        
    def _merge_keyword_results(self, similar_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """合并相似关键词的结果"""
        # 选择搜索结果最多的作为主关键词
        primary_result = max(similar_results, key=lambda x: x.get('search_results_count', 0))
        
        # 合并关键词列表
        merged_keywords = [result['keyword'] for result in similar_results]
        
        # 合并内容缺口
        all_content_gaps = []
        for result in similar_results:
            gaps = result.get('content_gaps', [])
            if isinstance(gaps, list):
                all_content_gaps.extend([str(gap) for gap in gaps])
            elif gaps:
                all_content_gaps.append(str(gaps))
        unique_content_gaps = list(set(all_content_gaps))
        
        # 合并已有内容类型
        all_content_types = []
        for result in similar_results:
            types = result.get('existing_content_types', [])
            if isinstance(types, list):
                all_content_types.extend([str(t) for t in types])
            elif types:
                all_content_types.append(str(types))
        unique_content_types = list(set(all_content_types))
        
        # 合并建议
        all_recommendations = []
        for result in similar_results:
            recs = result.get('recommendations', [])
            if isinstance(recs, list):
                all_recommendations.extend([str(rec) for rec in recs])
            elif recs:
                all_recommendations.append(str(recs))
        unique_recommendations = list(set(all_recommendations))
        
        # 计算平均置信度
        confidences = [result.get('confidence_score', 0) for result in similar_results]
        avg_confidence = sum(confidences) / len(confidences) if confidences else 0
        
        # 合并搜索意图
        search_intents = [result.get('search_intent', '') for result in similar_results if result.get('search_intent')]
        primary_intent = search_intents[0] if search_intents else ''
        
        return {
            'primary_keyword': primary_result['keyword'],
            'merged_keywords': merged_keywords,
            'keyword_count': len(merged_keywords),
            'search_intent': primary_intent,
            'content_gaps': unique_content_gaps,
            'existing_content_types': unique_content_types,
            'confidence_score': avg_confidence,
            'recommendations': unique_recommendations,
            'search_results_count': primary_result.get('search_results_count', 0),
            'total_analysis_time': sum(result.get('batch_analysis_time', 0) for result in similar_results),
            'merged_from': len(similar_results)
        }
