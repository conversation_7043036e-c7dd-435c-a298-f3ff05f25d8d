#!/usr/bin/env python3
"""
关键词分析主函数模块

整合Google搜索爬虫和Gemini分析功能，实现完整的关键词SERP分析流程。
"""

import asyncio
import time
from typing import Dict, List, Any, Optional
import logging
from dotenv import load_dotenv

from .google_scraper import GoogleScraper
from .gemini_analyzer import SERPAnalyzer

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class KeywordAnalyzer:
    """关键词分析器，整合搜索和分析功能"""
    
    def __init__(self, 
                 prompt_store_path: str = "./prompt_store",
                 max_search_results: int = 20,
                 scraping_delay: tuple = (1, 3)):
        """
        初始化关键词分析器
        
        Args:
            prompt_store_path: 提示词存储路径
            max_search_results: 最大搜索结果数量
            scraping_delay: 爬虫延迟时间范围
        """
        self.prompt_store_path = prompt_store_path
        self.max_search_results = max_search_results
        self.scraping_delay = scraping_delay
        
        # 初始化组件
        self.serp_analyzer = SERPAnalyzer(prompt_store_path)
        
    async def analyze_keyword(self, keyword: str) -> Dict[str, Any]:
        """
        分析单个关键词
        
        Args:
            keyword: 要分析的关键词
            
        Returns:
            分析结果字典，包含搜索意图、内容缺口、已有内容形式等
        """
        try:
            logger.info(f"开始分析关键词: {keyword}")
            start_time = time.time()
            
            # 步骤1: 爬取Google搜索结果
            logger.info(f"正在爬取关键词 '{keyword}' 的搜索结果...")
            search_results = await self._scrape_google_results(keyword)
            
            if not search_results:
                logger.warning(f"关键词 '{keyword}' 未获取到搜索结果")
                return self._create_empty_result(keyword, "未获取到搜索结果")
            
            logger.info(f"成功获取 {len(search_results)} 个搜索结果")
            
            # 步骤2: 使用Gemini分析搜索结果
            logger.info(f"正在分析关键词 '{keyword}' 的搜索结果...")
            analysis_result = await self.serp_analyzer.analyze_search_results(keyword, search_results)
            
            # 添加元数据
            analysis_result.update({
                'keyword': keyword,
                'search_results_count': len(search_results),
                'analysis_time': time.time() - start_time,
                'timestamp': time.time(),
                'search_results': search_results  # 保留原始搜索结果
            })
            
            logger.info(f"关键词 '{keyword}' 分析完成，耗时 {analysis_result['analysis_time']:.2f} 秒")
            return analysis_result
            
        except Exception as e:
            logger.error(f"分析关键词 '{keyword}' 失败: {e}")
            return self._create_error_result(keyword, str(e))
            
    async def _scrape_google_results(self, keyword: str) -> List[Dict[str, str]]:
        """爬取Google搜索结果"""
        try:
            async with GoogleScraper(delay_range=self.scraping_delay) as scraper:
                return await scraper.search_google(keyword, self.max_search_results)
        except Exception as e:
            logger.error(f"爬取搜索结果失败: {e}")
            return []
            
    def _create_empty_result(self, keyword: str, reason: str) -> Dict[str, Any]:
        """创建空结果"""
        return {
            'keyword': keyword,
            'search_intent': f"无法分析 - {reason}",
            'content_gaps': [],
            'existing_content_types': [],
            'confidence_score': 0.0,
            'recommendations': [f"建议重新尝试分析关键词: {keyword}"],
            'search_results_count': 0,
            'analysis_time': 0,
            'timestamp': time.time(),
            'error': reason,
            'search_results': []
        }
        
    def _create_error_result(self, keyword: str, error_msg: str) -> Dict[str, Any]:
        """创建错误结果"""
        return {
            'keyword': keyword,
            'search_intent': f"分析失败 - {error_msg}",
            'content_gaps': [],
            'existing_content_types': [],
            'confidence_score': 0.0,
            'recommendations': [f"分析出错，请检查网络连接和API配置"],
            'search_results_count': 0,
            'analysis_time': 0,
            'timestamp': time.time(),
            'error': error_msg,
            'search_results': []
        }
        
    async def close(self):
        """关闭分析器"""
        if self.serp_analyzer:
            await self.serp_analyzer.close()


async def analyze_single_keyword(keyword: str, 
                                prompt_store_path: str = "./prompt_store",
                                max_results: int = 20) -> Dict[str, Any]:
    """
    分析单个关键词的便捷函数
    
    Args:
        keyword: 要分析的关键词
        prompt_store_path: 提示词存储路径
        max_results: 最大搜索结果数量
        
    Returns:
        分析结果字典
    """
    analyzer = KeywordAnalyzer(
        prompt_store_path=prompt_store_path,
        max_search_results=max_results
    )
    
    try:
        return await analyzer.analyze_keyword(keyword)
    finally:
        await analyzer.close()


async def analyze_multiple_keywords(keywords: List[str],
                                  prompt_store_path: str = "./prompt_store",
                                  max_results: int = 20,
                                  delay_between_keywords: float = 5.0) -> List[Dict[str, Any]]:
    """
    分析多个关键词的便捷函数
    
    Args:
        keywords: 关键词列表
        prompt_store_path: 提示词存储路径
        max_results: 最大搜索结果数量
        delay_between_keywords: 关键词间延迟时间
        
    Returns:
        分析结果列表
    """
    analyzer = KeywordAnalyzer(
        prompt_store_path=prompt_store_path,
        max_search_results=max_results
    )
    
    results = []
    
    try:
        for i, keyword in enumerate(keywords):
            logger.info(f"处理关键词 {i+1}/{len(keywords)}: {keyword}")
            
            result = await analyzer.analyze_keyword(keyword)
            results.append(result)
            
            # 添加延迟避免被限制
            if i < len(keywords) - 1:
                logger.info(f"等待 {delay_between_keywords} 秒后处理下一个关键词...")
                await asyncio.sleep(delay_between_keywords)
                
    finally:
        await analyzer.close()
        
    return results


# 测试代码
if __name__ == "__main__":
    async def test():
        # 测试单个关键词分析
        result = await analyze_single_keyword("python programming")
        
        print("=== 关键词分析结果 ===")
        print(f"关键词: {result['keyword']}")
        print(f"搜索意图: {result['search_intent']}")
        print(f"内容缺口: {result['content_gaps']}")
        print(f"已有内容类型: {result['existing_content_types']}")
        print(f"置信度: {result['confidence_score']}")
        print(f"建议: {result['recommendations']}")
        print(f"分析时间: {result['analysis_time']:.2f} 秒")
        
    asyncio.run(test())
