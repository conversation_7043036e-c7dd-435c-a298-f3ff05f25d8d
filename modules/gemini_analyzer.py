#!/usr/bin/env python3
"""
Gemini分析智能体模块

使用autogen v0.4和prompt_manager系统创建Gemini智能体，
用于分析搜索结果并提取用户搜索意图、内容缺口、已有内容形式。
"""

import asyncio
import json
import os
from typing import Dict, List, Any, Optional
import logging
from dotenv import load_dotenv

# 导入autogen v0.4组件
from autogen_agentchat.agents import AssistantAgent
from autogen_ext.models.openai import OpenAIChatCompletionClient
import google.generativeai as genai

# 导入prompt_manager
from prompt_manager import PromptManager

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class GeminiChatCompletionClient:
    """Gemini聊天完成客户端，兼容autogen接口"""

    def __init__(self, model_name: str = "gemini-2.5-flash", api_key: Optional[str] = None):
        """
        初始化Gemini客户端

        Args:
            model_name: 模型名称
            api_key: API密钥
        """
        self.model_name = model_name
        self.api_key = api_key or os.getenv('GEMINI_API_KEY')

        if not self.api_key:
            raise ValueError("GEMINI_API_KEY环境变量未设置")

        # 配置Gemini
        genai.configure(api_key=self.api_key)
        self.model = genai.GenerativeModel(model_name)

    async def create(self, messages: List[Dict[str, str]], **kwargs) -> Any:
        """
        创建聊天完成，兼容autogen接口

        Args:
            messages: 消息列表
            **kwargs: 其他参数

        Returns:
            响应结果
        """
        try:
            # 将消息转换为Gemini格式
            prompt = self._convert_messages_to_prompt(messages)

            # 调用Gemini API
            response = await asyncio.to_thread(
                self.model.generate_content,
                prompt,
                generation_config=genai.types.GenerationConfig(
                    temperature=kwargs.get('temperature', 0.7),
                    max_output_tokens=kwargs.get('max_tokens', 4000),
                )
            )

            # 创建响应对象
            class GeminiResponse:
                def __init__(self, content: str):
                    self.content = content

            return GeminiResponse(response.text)

        except Exception as e:
            logger.error(f"Gemini API调用失败: {e}")
            raise

    def _convert_messages_to_prompt(self, messages: List[Dict[str, str]]) -> str:
        """将autogen消息格式转换为Gemini提示词"""
        prompt_parts = []

        for message in messages:
            role = message.get('role', '')
            content = message.get('content', '')

            if role == 'system':
                prompt_parts.append(f"系统指令: {content}")
            elif role == 'user':
                prompt_parts.append(f"用户: {content}")
            elif role == 'assistant':
                prompt_parts.append(f"助手: {content}")

        return "\n\n".join(prompt_parts)

    async def close(self):
        """关闭客户端（Gemini不需要显式关闭）"""
        pass


class SERPAnalyzer:
    """SERP分析器，使用Gemini分析搜索结果"""

    def __init__(self, prompt_store_path: str = "./prompt_store"):
        """
        初始化SERP分析器

        Args:
            prompt_store_path: 提示词存储路径
        """
        self.prompt_store_path = prompt_store_path
        self.prompt_manager = None
        self.gemini_client = None
        self._setup_prompt_manager()
        self._setup_gemini()

    def _setup_prompt_manager(self):
        """设置提示词管理器"""
        try:
            self.prompt_manager = PromptManager(self.prompt_store_path)
            logger.info("提示词管理器初始化成功")
        except Exception as e:
            logger.error(f"提示词管理器初始化失败: {e}")
            raise

    def _setup_gemini(self):
        """设置Gemini客户端"""
        try:
            api_key = os.getenv('GEMINI_API_KEY')
            if not api_key:
                raise ValueError("GEMINI_API_KEY环境变量未设置")

            genai.configure(api_key=api_key)
            self.gemini_client = genai.GenerativeModel("gemini-2.5-flash")

            logger.info("Gemini客户端初始化成功")

        except Exception as e:
            logger.error(f"Gemini客户端初始化失败: {e}")
            raise
            
    async def analyze_search_results(self, keyword: str, search_results: List[Dict[str, str]]) -> Dict[str, Any]:
        """
        分析搜索结果
        
        Args:
            keyword: 搜索关键词
            search_results: 搜索结果列表
            
        Returns:
            分析结果字典
        """
        try:
            # 格式化搜索结果
            formatted_results = self._format_search_results(search_results)
            
            # 获取用户提示词模板
            prompts = self.prompt_manager.get_prompts('serp_analyzer')
            user_prompt = prompts['user'].format(
                keyword=keyword,
                search_results=formatted_results
            )
            
            # 调用智能体分析
            logger.info(f"开始分析关键词: {keyword}")
            response = await self.agent.run(task=user_prompt)
            
            # 解析响应
            analysis_result = self._parse_analysis_response(response, keyword)
            
            logger.info(f"关键词 {keyword} 分析完成")
            return analysis_result
            
        except Exception as e:
            logger.error(f"分析搜索结果失败: {e}")
            raise
            
    def _format_search_results(self, search_results: List[Dict[str, str]]) -> str:
        """格式化搜索结果为文本"""
        formatted = []
        
        for i, result in enumerate(search_results, 1):
            formatted.append(f"""
{i}. 标题: {result.get('title', 'N/A')}
   URL: {result.get('url', 'N/A')}
   描述: {result.get('description', 'N/A')}
""")
        
        return "\n".join(formatted)
        
    def _parse_analysis_response(self, response: str, keyword: str) -> Dict[str, Any]:
        """解析分析响应"""
        try:
            # 尝试解析JSON响应
            if isinstance(response, str):
                # 查找JSON部分
                start_idx = response.find('{')
                end_idx = response.rfind('}') + 1
                
                if start_idx != -1 and end_idx != 0:
                    json_str = response[start_idx:end_idx]
                    result = json.loads(json_str)
                else:
                    # 如果没有找到JSON，创建结构化结果
                    result = self._create_structured_result(response, keyword)
            else:
                result = response
                
            # 确保包含所有必需字段
            required_fields = ['search_intent', 'content_gaps', 'existing_content_types', 'confidence_score', 'recommendations']
            for field in required_fields:
                if field not in result:
                    result[field] = "未提供" if field != 'confidence_score' else 0.5
                    
            return result
            
        except json.JSONDecodeError:
            logger.warning("无法解析JSON响应，使用文本解析")
            return self._create_structured_result(response, keyword)
            
    def _create_structured_result(self, response: str, keyword: str) -> Dict[str, Any]:
        """从文本响应创建结构化结果"""
        return {
            'keyword': keyword,
            'search_intent': response[:500] + "..." if len(response) > 500 else response,
            'content_gaps': ["基于响应文本识别的内容缺口"],
            'existing_content_types': ["基于响应文本识别的内容类型"],
            'confidence_score': 0.7,
            'recommendations': ["基于分析提供的建议"],
            'raw_response': response
        }
        
    async def close(self):
        """关闭分析器"""
        if self.agent and hasattr(self.agent, 'model_client'):
            await self.agent.model_client.close()


# 便捷函数
async def analyze_keyword_serp(keyword: str, search_results: List[Dict[str, str]]) -> Dict[str, Any]:
    """
    分析关键词SERP的便捷函数
    
    Args:
        keyword: 关键词
        search_results: 搜索结果
        
    Returns:
        分析结果
    """
    analyzer = SERPAnalyzer()
    try:
        return await analyzer.analyze_search_results(keyword, search_results)
    finally:
        await analyzer.close()


# 测试代码
if __name__ == "__main__":
    async def test():
        # 模拟搜索结果
        test_results = [
            {
                'title': 'Python Programming Tutorial',
                'url': 'https://example.com/python-tutorial',
                'description': 'Learn Python programming from basics to advanced concepts.'
            },
            {
                'title': 'Python Official Documentation',
                'url': 'https://docs.python.org/',
                'description': 'Official Python documentation and reference.'
            }
        ]
        
        result = await analyze_keyword_serp("python programming", test_results)
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
    asyncio.run(test())
