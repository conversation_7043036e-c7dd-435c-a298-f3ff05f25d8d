#!/usr/bin/env python3
"""
关键词处理和去重合并模块

读取CSV文件，处理关键词列表，实现相似关键词的智能合并。
"""

import pandas as pd
import asyncio
import json
import time
from typing import List, Dict, Any, Tuple, Optional
from difflib import SequenceMatcher
import logging
from pathlib import Path

from .keyword_analyzer import analyze_multiple_keywords

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class KeywordProcessor:
    """关键词处理器，负责CSV处理和关键词去重合并"""
    
    def __init__(self, 
                 similarity_threshold: float = 0.8,
                 intent_similarity_threshold: float = 0.9,
                 delay_between_keywords: float = 5.0):
        """
        初始化关键词处理器
        
        Args:
            similarity_threshold: 关键词文本相似度阈值
            intent_similarity_threshold: 搜索意图相似度阈值
            delay_between_keywords: 关键词分析间延迟时间
        """
        self.similarity_threshold = similarity_threshold
        self.intent_similarity_threshold = intent_similarity_threshold
        self.delay_between_keywords = delay_between_keywords
        
    def load_keywords_from_csv(self, csv_path: str, keyword_column: str = "Generated Keyword") -> List[str]:
        """
        从CSV文件加载关键词列表
        
        Args:
            csv_path: CSV文件路径
            keyword_column: 关键词列名
            
        Returns:
            去重后的关键词列表
        """
        try:
            logger.info(f"正在加载CSV文件: {csv_path}")
            
            # 读取CSV文件
            df = pd.read_csv(csv_path)
            
            # 检查列是否存在
            if keyword_column not in df.columns:
                raise ValueError(f"CSV文件中未找到列: {keyword_column}")
                
            # 提取关键词并去重
            keywords = df[keyword_column].dropna().unique().tolist()
            
            logger.info(f"成功加载 {len(keywords)} 个唯一关键词")
            return keywords
            
        except Exception as e:
            logger.error(f"加载CSV文件失败: {e}")
            raise
            
    async def process_keywords(self, 
                             keywords: List[str],
                             prompt_store_path: str = "./prompt_store") -> List[Dict[str, Any]]:
        """
        处理关键词列表，进行分析和去重合并
        
        Args:
            keywords: 关键词列表
            prompt_store_path: 提示词存储路径
            
        Returns:
            处理后的分析结果列表
        """
        try:
            logger.info(f"开始处理 {len(keywords)} 个关键词")
            
            # 步骤1: 分析所有关键词
            logger.info("步骤1: 分析所有关键词...")
            analysis_results = await analyze_multiple_keywords(
                keywords=keywords,
                prompt_store_path=prompt_store_path,
                delay_between_keywords=self.delay_between_keywords
            )
            
            # 步骤2: 识别相似关键词并合并
            logger.info("步骤2: 识别相似关键词并合并...")
            merged_results = self._merge_similar_keywords(analysis_results)
            
            logger.info(f"处理完成，从 {len(keywords)} 个关键词合并为 {len(merged_results)} 个结果")
            return merged_results
            
        except Exception as e:
            logger.error(f"处理关键词失败: {e}")
            raise
            
    def _merge_similar_keywords(self, analysis_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """合并相似的关键词分析结果"""
        merged_results = []
        processed_indices = set()
        
        for i, result in enumerate(analysis_results):
            if i in processed_indices:
                continue
                
            # 查找与当前关键词相似的其他关键词
            similar_results = [result]
            similar_indices = {i}
            
            for j, other_result in enumerate(analysis_results[i+1:], i+1):
                if j in processed_indices:
                    continue
                    
                if self._are_keywords_similar(result, other_result):
                    similar_results.append(other_result)
                    similar_indices.add(j)
                    
            # 合并相似的结果
            if len(similar_results) > 1:
                merged_result = self._merge_keyword_results(similar_results)
                merged_results.append(merged_result)
                logger.info(f"合并了 {len(similar_results)} 个相似关键词: {[r['keyword'] for r in similar_results]}")
            else:
                merged_results.append(result)
                
            # 标记已处理的索引
            processed_indices.update(similar_indices)
            
        return merged_results
        
    def _are_keywords_similar(self, result1: Dict[str, Any], result2: Dict[str, Any]) -> bool:
        """判断两个关键词是否相似"""
        keyword1 = result1['keyword'].lower()
        keyword2 = result2['keyword'].lower()

        # 计算文本相似度
        text_similarity = SequenceMatcher(None, keyword1, keyword2).ratio()

        # 计算搜索意图相似度
        intent1 = result1.get('search_intent', '')
        intent2 = result2.get('search_intent', '')

        # 处理搜索意图可能是字典的情况
        if isinstance(intent1, dict):
            intent1 = str(intent1.get('primary_intent', '')) + ' ' + str(intent1.get('details', ''))
        if isinstance(intent2, dict):
            intent2 = str(intent2.get('primary_intent', '')) + ' ' + str(intent2.get('details', ''))

        intent1 = str(intent1).lower()
        intent2 = str(intent2).lower()

        intent_similarity = SequenceMatcher(None, intent1, intent2).ratio()

        # 判断是否相似
        is_text_similar = text_similarity >= self.similarity_threshold
        is_intent_similar = intent_similarity >= self.intent_similarity_threshold

        # 如果文本非常相似或者意图非常相似，则认为是相似关键词
        return is_text_similar or is_intent_similar
        
    def _merge_keyword_results(self, similar_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """合并多个相似关键词的分析结果"""
        # 选择置信度最高的结果作为主结果
        primary_result = max(similar_results, key=lambda x: x.get('confidence_score', 0))
        
        # 合并关键词列表
        merged_keywords = [result['keyword'] for result in similar_results]
        
        # 合并内容缺口
        all_content_gaps = []
        for result in similar_results:
            gaps = result.get('content_gaps', [])
            if isinstance(gaps, list):
                all_content_gaps.extend([str(gap) for gap in gaps])
            elif gaps:
                all_content_gaps.append(str(gaps))
        unique_content_gaps = list(set(all_content_gaps))

        # 合并已有内容类型
        all_content_types = []
        for result in similar_results:
            types = result.get('existing_content_types', [])
            if isinstance(types, list):
                all_content_types.extend([str(t) for t in types])
            elif types:
                all_content_types.append(str(types))
        unique_content_types = list(set(all_content_types))

        # 合并建议
        all_recommendations = []
        for result in similar_results:
            recs = result.get('recommendations', [])
            if isinstance(recs, list):
                all_recommendations.extend([str(rec) for rec in recs])
            elif recs:
                all_recommendations.append(str(recs))
        unique_recommendations = list(set(all_recommendations))
        
        # 计算平均置信度
        confidence_scores = [result.get('confidence_score', 0) for result in similar_results]
        avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0
        
        # 创建合并后的结果
        merged_result = {
            'primary_keyword': primary_result['keyword'],
            'merged_keywords': merged_keywords,
            'keyword_count': len(merged_keywords),
            'search_intent': primary_result.get('search_intent', ''),
            'content_gaps': unique_content_gaps,
            'existing_content_types': unique_content_types,
            'confidence_score': avg_confidence,
            'recommendations': unique_recommendations,
            'search_results_count': sum(result.get('search_results_count', 0) for result in similar_results),
            'total_analysis_time': sum(result.get('analysis_time', 0) for result in similar_results),
            'timestamp': time.time(),
            'merged_from': len(similar_results),
            'original_results': similar_results  # 保留原始结果用于参考
        }
        
        return merged_result
        
    def save_results_to_csv(self, results: List[Dict[str, Any]], output_path: str):
        """将结果保存为CSV文件"""
        try:
            logger.info(f"正在保存结果到: {output_path}")
            
            # 准备数据
            rows = []
            for result in results:
                # 安全地处理可能是字典或其他类型的字段
                def safe_join(value, default=''):
                    if isinstance(value, list):
                        return '; '.join([str(item) for item in value])
                    elif value:
                        return str(value)
                    else:
                        return default

                # 处理搜索意图字段
                search_intent = result.get('search_intent', '')
                if isinstance(search_intent, dict):
                    search_intent = str(search_intent.get('primary_intent', '')) + ' - ' + str(search_intent.get('details', ''))
                else:
                    search_intent = str(search_intent)

                row = {
                    'Primary_Keyword': result.get('primary_keyword', result.get('keyword', '')),
                    'Merged_Keywords': safe_join(result.get('merged_keywords', [result.get('keyword', '')])),
                    'Keyword_Count': result.get('keyword_count', 1),
                    'Search_Intent': search_intent,
                    'Content_Gaps': safe_join(result.get('content_gaps', [])),
                    'Existing_Content_Types': safe_join(result.get('existing_content_types', [])),
                    'Confidence_Score': result.get('confidence_score', 0),
                    'Recommendations': safe_join(result.get('recommendations', [])),
                    'Search_Results_Count': result.get('search_results_count', 0),
                    'Analysis_Time': result.get('analysis_time', result.get('total_analysis_time', 0)),
                    'Merged_From': result.get('merged_from', 1)
                }
                rows.append(row)
                
            # 创建DataFrame并保存
            df = pd.DataFrame(rows)
            df.to_csv(output_path, index=False, encoding='utf-8-sig')
            
            logger.info(f"成功保存 {len(rows)} 条结果到 {output_path}")
            
        except Exception as e:
            logger.error(f"保存结果失败: {e}")
            raise


async def process_csv_keywords(csv_path: str,
                             output_path: str,
                             keyword_column: str = "Generated Keyword",
                             prompt_store_path: str = "./prompt_store",
                             similarity_threshold: float = 0.8,
                             delay_between_keywords: float = 5.0) -> List[Dict[str, Any]]:
    """
    处理CSV文件中的关键词的便捷函数
    
    Args:
        csv_path: 输入CSV文件路径
        output_path: 输出CSV文件路径
        keyword_column: 关键词列名
        prompt_store_path: 提示词存储路径
        similarity_threshold: 相似度阈值
        delay_between_keywords: 关键词间延迟
        
    Returns:
        处理结果列表
    """
    processor = KeywordProcessor(
        similarity_threshold=similarity_threshold,
        delay_between_keywords=delay_between_keywords
    )
    
    # 加载关键词
    keywords = processor.load_keywords_from_csv(csv_path, keyword_column)
    
    # 处理关键词
    results = await processor.process_keywords(keywords, prompt_store_path)
    
    # 保存结果
    processor.save_results_to_csv(results, output_path)
    
    return results


# 测试代码
if __name__ == "__main__":
    async def test():
        # 测试处理少量关键词
        test_keywords = ["python programming", "python coding", "learn python"]
        
        processor = KeywordProcessor(similarity_threshold=0.7)
        results = await processor.process_keywords(test_keywords)
        
        print("=== 处理结果 ===")
        for result in results:
            print(f"主关键词: {result.get('primary_keyword', result.get('keyword'))}")
            print(f"合并关键词: {result.get('merged_keywords', [])}")
            print(f"搜索意图: {result.get('search_intent', '')[:100]}...")
            print("---")
            
    asyncio.run(test())
