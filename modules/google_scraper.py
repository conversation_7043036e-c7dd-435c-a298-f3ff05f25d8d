#!/usr/bin/env python3
"""
Google搜索爬虫模块

使用playwright实现Google搜索结果爬取，获取前20个搜索结果的详细信息。
"""

import asyncio
import random
import time
from typing import List, Dict, Optional
from playwright.async_api import async_playwright, Browser, Page
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class GoogleScraper:
    """Google搜索结果爬虫类"""
    
    def __init__(self, headless: bool = True, delay_range: tuple = (1, 3)):
        """
        初始化爬虫
        
        Args:
            headless: 是否使用无头模式
            delay_range: 请求间隔时间范围(秒)
        """
        self.headless = headless
        self.delay_range = delay_range
        self.browser: Optional[Browser] = None
        self.page: Optional[Page] = None
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.start()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
        
    async def start(self):
        """启动浏览器"""
        try:
            self.playwright = await async_playwright().start()
            self.browser = await self.playwright.chromium.launch(
                headless=self.headless,
                args=[
                    '--no-sandbox',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-dev-shm-usage',
                    '--disable-gpu',
                    '--no-first-run',
                    '--no-default-browser-check',
                    '--disable-extensions'
                ]
            )
            
            # 创建新页面
            self.page = await self.browser.new_page()
            
            # 设置用户代理
            await self.page.set_extra_http_headers({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 '
                             '(KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            })

            # 设置视口
            await self.page.set_viewport_size({"width": 1920, "height": 1080})
            
            logger.info("浏览器启动成功")
            
        except Exception as e:
            logger.error(f"启动浏览器失败: {e}")
            raise
            
    async def close(self):
        """关闭浏览器"""
        try:
            if self.page:
                await self.page.close()
            if self.browser:
                await self.browser.close()
            if hasattr(self, 'playwright'):
                await self.playwright.stop()
            logger.info("浏览器关闭成功")
        except Exception as e:
            logger.error(f"关闭浏览器失败: {e}")
            
    async def search_google(self, keyword: str, max_results: int = 20) -> List[Dict[str, str]]:
        """
        搜索Google并获取结果
        
        Args:
            keyword: 搜索关键词
            max_results: 最大结果数量
            
        Returns:
            搜索结果列表，每个结果包含title, url, description
        """
        if not self.page:
            raise RuntimeError("浏览器未启动，请先调用start()方法")
            
        try:
            logger.info(f"开始搜索关键词: {keyword}")
            
            # 访问Google搜索页面
            await self.page.goto('https://www.google.com', wait_until='networkidle')
            
            # 随机延迟
            await self._random_delay()
            
            # 查找搜索框并输入关键词
            search_box = await self.page.wait_for_selector('textarea[name="q"], input[name="q"]')
            await search_box.fill(keyword)
            await search_box.press('Enter')
            
            # 等待搜索结果加载
            await self.page.wait_for_selector('div#search', timeout=10000)
            await self._random_delay()
            
            # 提取搜索结果
            results = await self._extract_search_results(max_results)
            
            logger.info(f"成功获取 {len(results)} 个搜索结果")
            return results
            
        except Exception as e:
            logger.error(f"搜索失败: {e}")
            raise
            
    async def _extract_search_results(self, max_results: int) -> List[Dict[str, str]]:
        """提取搜索结果"""
        results = []

        try:
            # 等待页面加载完成
            await asyncio.sleep(2)

            # 使用JavaScript直接提取搜索结果
            search_results = await self.page.evaluate("""
                () => {
                    const results = [];

                    // 尝试多种选择器
                    const selectors = [
                        'div.g',
                        'div.MjjYud',
                        'div[data-ved]'
                    ];

                    let elements = [];
                    for (const selector of selectors) {
                        elements = document.querySelectorAll(selector);
                        if (elements.length > 0) break;
                    }

                    for (let i = 0; i < Math.min(elements.length, 20); i++) {
                        const element = elements[i];

                        // 查找标题
                        const titleElement = element.querySelector('h3');
                        const title = titleElement ? titleElement.innerText.trim() : '';

                        // 查找链接
                        const linkElement = element.querySelector('a[href]');
                        let url = linkElement ? linkElement.href : '';

                        // 清理Google重定向URL
                        if (url.includes('/url?q=')) {
                            try {
                                url = decodeURIComponent(url.split('/url?q=')[1].split('&')[0]);
                            } catch (e) {
                                continue;
                            }
                        }

                        // 查找描述
                        const descSelectors = ['.VwiC3b', '.s3v9rd', '.st', 'span[data-ved]'];
                        let description = '';
                        for (const descSel of descSelectors) {
                            const descElement = element.querySelector(descSel);
                            if (descElement) {
                                description = descElement.innerText.trim();
                                break;
                            }
                        }

                        // 验证结果
                        if (title && url && url.startsWith('http') && !url.includes('javascript:')) {
                            results.push({
                                title: title,
                                url: url,
                                description: description
                            });
                        }
                    }

                    return results;
                }
            """)

            # 限制结果数量
            results = search_results[:max_results]

        except Exception as e:
            logger.error(f"提取搜索结果失败: {e}")

        return results
        

            
    async def _random_delay(self):
        """随机延迟"""
        delay = random.uniform(*self.delay_range)
        await asyncio.sleep(delay)


async def search_keyword(keyword: str, max_results: int = 20) -> List[Dict[str, str]]:
    """
    搜索关键词的便捷函数
    
    Args:
        keyword: 搜索关键词
        max_results: 最大结果数量
        
    Returns:
        搜索结果列表
    """
    async with GoogleScraper() as scraper:
        return await scraper.search_google(keyword, max_results)


# 测试代码
if __name__ == "__main__":
    async def test():
        results = await search_keyword("python programming", 5)
        for i, result in enumerate(results, 1):
            print(f"{i}. {result['title']}")
            print(f"   URL: {result['url']}")
            print(f"   描述: {result['description'][:100]}...")
            print()
            
    asyncio.run(test())
